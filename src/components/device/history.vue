<template>
    <div class="history_content ">
        <div class="box-list">
            <el-scrollbar>
                <div v-for="(item, i) in data" :key="i" class="box-list-item" 
                     :class="{ 'active-item': activeBox == item.standardId }"
                     @click="changeBox(item, i)">
                    <div class="icon" :class="{ active: activeBox == item.standardId }"></div>
                    <div class="content-wrapper">
                        <div class="header">
                            <div class="box-list-title">{{ item.name }}</div>
                        </div>
                        <div class="item">
                            <div class="label">平均值</div>
                            <div class="box-list-num">
                                {{ values[item.variable] ? values[item.variable].mean : 0 }}
                            </div>
                        </div>
                    </div>
                    <div class="arrow">
                        <img src="@/components/device/img/arrow.png" />
                    </div>
                    <div class="bar top_bar"></div>
                    <div class="bar bottom_bar"></div>
                </div>

            </el-scrollbar>
        </div>
        <div class="chart">
            <!-- <panel :title="item?.name"> -->
            <el-form :inline="true" class="search_box form_inline" size="small">
                <el-form-item label="时间选择">
                    <el-date-picker :popper-class="search_picker" v-model="startEndDate" type="daterange"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        @change="changeDate"></el-date-picker>
                </el-form-item>
            </el-form>
            <!-- </panel> -->
            <!-- <el-form :inline="true" class="search_sub" size="small" style="margin-right:15px">
            <el-button type="primary" size="small" @click="down">导出</el-button>
        </el-form> -->

            <historyChart :historyData="historyData" />
        </div>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import historyChart from '@/components/echarts/historyChart.vue'
import {
    reactive,
    toRefs,
    onMounted,
    watch
} from 'vue'
import {
    ElMessage
} from 'element-plus'
import Panel from './Panel.vue'
export default {
    components: {
        historyChart,
        Panel
    },
    props: ['deviceId'],
    setup(props) {
        const api = inject('$api')
        const state = reactive({
            data: [],
            values: {},
            checked: false,
            chartsData: {},
            interval: null,
            chart: null,
            activeBox: '',
            historyData: {
                data: [],
                time: [],
                name: '',
            },
            realParams: {
                title: '',
                unit: '',
            },
            startEndDate: [],
            item: null,
            downData: []
        })
        watch(() => props.deviceId, (val) => {
            if (val) {
                getDeviceParams()
            }
        })
        onMounted(() => {
            state.startEndDate.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.startEndDate.push(dayjs().format('YYYY-MM-DD 23:59:59'))
            if (props.deviceId) {
                getDeviceParams()
            }
        })

        const getDeviceParams = () => {
            api.getDeviceStandard({
                deviceId: props.deviceId,
            }).then((res) => {
                // let data = [];

                // if (res.data && res.data.length > 0) {
                //     res.data.forEach((d) => {
                //         if (d.standardParams && d.standardParams.length > 0) {
                //             for (let i = 0; i < d.standardParams.length; i++) {
                //                 if (d.standardParams[i].dataType == 'string_input') {
                //                     data.push(d)
                //                     break
                //                 }
                //             }
                //         }
                //     })
                // }

                // state.data = data
                state.data = res.data
                analyze(state.data)
                changeBox(res.data[0])
            })
        }
        const analyze = (data) => {
            if (data) {
                let key = []
                data.forEach((d) => {
                    if (d.variable) {
                        key.push(d.variable)
                        state.values = Object.assign({}, state.values, {
                            [d.variable]: {
                                mean: 0,
                                max: 0,
                                min: 0,
                            },
                        })
                    }
                })
                if (key.length > 0) {
                    api.getAnalyze({
                        keyword: key,
                        bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD 00:00:00"),
                        et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD 23:59:59"),
                    }).then((res) => {
                        res.data.forEach((d) => {
                            state.values[d.variable].max = d.max
                            state.values[d.variable].min = d.min
                            state.values[d.variable].mean = d.mean
                        })
                    })
                }
            }
        }
        const changeBox = (item) => {
            if (item) {
                state.item = item
                state.activeBox = item.standardId
                getHistory(item.variable, item.name)
            }

        }
        const getHistory = (variable, name) => {
            api.getHistoryDataNoPage({
                bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD HH:mm:ss"),
                variable: variable,
            }).then((res) => {
                state.downData = res.data.histories;
                state.historyData.time = res.data.map((v, i) => {
                    return v.ts
                })
                state.historyData.data = res.data.map((e, j) => {
                    return e._value
                })
                state.historyData.name = name
            })
        }

        const down = () => {
            if (!state.item) {
                ElMessage.warning("无历史指标数据");
                return;
            }
            const params = {
                bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD 23:59:59"),
                keyword: state.item.variable,
            }
            api.getHistoryDown(params).then(res => {
                const link = document.createElement('a')
                const blob = new Blob([res], {
                    type: 'application/vnd.ms-excel'
                })
                link.style.display = 'none'
                link.href = URL.createObjectURL(blob)
                link.setAttribute('download', `历史数据.xlsx`)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            })
        }
        const changeDate = (value) => {
            if (state.item && state.item.variable) {
                getHistory(state.item.variable, state.item.name)
            }
        }

        return {
            ...toRefs(state),
            getDeviceParams,
            analyze,
            changeBox,
            getHistory,
            changeDate,
            down
        }
    },
}
</script>

<style lang="scss" scoped>
.history_content {
    display: flex;
    flex-direction: row;
    gap: 20px;
    background: linear-gradient(135deg, rgba(5, 26, 48, 0.4) 0%, rgba(2, 15, 30, 0.6) 100%);
    border: 1px solid rgba(45, 85, 135, 0.3);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

    .box-list {
        width: 320px;
        height: calc(100% - 20px);

        :deep(.el-scrollbar) {
            .el-scrollbar__bar {
                &.is-vertical {
                    .el-scrollbar__thumb {
                        background: linear-gradient(135deg, rgba(45, 135, 230, 0.8) 0%, rgba(25, 118, 210, 0.6) 100%);
                        border-radius: 4px;
                        transition: all 0.3s ease;

                        &:hover {
                            background: linear-gradient(135deg, rgba(45, 135, 230, 1) 0%, rgba(25, 118, 210, 0.8) 100%);
                        }
                    }
                }
            }

            .el-scrollbar__track {
                background: rgba(5, 26, 48, 0.5);
                border-radius: 4px;
            }
        }

        .box-list-item {
            background: linear-gradient(135deg, 
                rgba(15, 35, 65, 0.9) 0%, 
                rgba(8, 25, 45, 0.95) 50%,
                rgba(5, 20, 40, 0.9) 100%);
            border: 1px solid rgba(45, 85, 135, 0.4);
            border-left: 4px solid transparent;
            border-radius: 12px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            box-shadow: 
                0 4px 20px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, 
                    rgba(45, 135, 230, 0.12) 0%, 
                    rgba(25, 118, 210, 0.08) 50%,
                    transparent 100%);
                opacity: 0;
                transition: all 0.4s ease;
            }

            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, 
                    transparent 0%, 
                    rgba(255, 255, 255, 0.1) 50%, 
                    transparent 100%);
                transition: left 0.6s ease;
            }

            &:hover {
                border-color: rgba(45, 135, 230, 0.7);
                border-left-color: #2d87e6;
                box-shadow: 
                    0 8px 30px rgba(45, 135, 230, 0.25),
                    0 2px 10px rgba(0, 0, 0, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.15);
                transform: translateY(-3px) scale(1.02);

                &::before {
                    opacity: 1;
                }

                &::after {
                    left: 100%;
                }

                .icon {
                    transform: scale(1.05);
                    filter: drop-shadow(0 0 12px rgba(45, 135, 230, 0.6));
                }

                .content-wrapper .item .box-list-num {
                    color: #4db8ff;
                    text-shadow: 0 0 12px rgba(77, 184, 255, 0.6);
                    transform: scale(1.05);
                }
            }

            // 选中状态
            &.active-item {
                border-left-color: #2d87e6;
                background: linear-gradient(135deg, 
                    rgba(45, 135, 230, 0.15) 0%, 
                    rgba(15, 35, 65, 0.9) 30%,
                    rgba(8, 25, 45, 0.95) 70%,
                    rgba(5, 20, 40, 0.9) 100%);
                box-shadow: 
                    0 6px 25px rgba(45, 135, 230, 0.3),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);

                &::before {
                    opacity: 0.8;
                    background: linear-gradient(135deg, 
                        rgba(45, 135, 230, 0.2) 0%, 
                        rgba(25, 118, 210, 0.15) 50%,
                        transparent 100%);
                }

                .bar {
                    box-shadow: 0 0 10px rgba(45, 135, 230, 0.8);
                    background: linear-gradient(90deg, #4db8ff 0%, #2d87e6 100%);
                }
            }

            .icon {
                width: 65px;
                height: 70px;
                background: url("@/components/device/img/t.png") no-repeat;
                background-size: contain;
                background-position: center;
                margin-right: 15px;
                position: relative;
                z-index: 2;
                filter: drop-shadow(0 2px 8px rgba(45, 135, 230, 0.4));
                transition: all 0.3s ease;
            }

            .content-wrapper {
                display: flex;
                flex-direction: column;
                flex: 1;
                position: relative;
                z-index: 2;
                gap: 6px;

                .header {
                    display: flex;
                    align-items: center;

                    .box-list-title {
                        font-size: 13px;
                        font-family: "Alibaba-PuHuiTi";
                        font-weight: 600;
                        color: #e6f4ff;
                        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
                        letter-spacing: 0.5px;
                        line-height: 1.4;
                    }
                }

                .item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 11px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 500;
                    color: rgba(230, 244, 255, 0.8);
                    padding: 2px 0;

                    .label {
                        font-size: 12px;
                        color: rgba(230, 244, 255, 0.7);
                        letter-spacing: 0.3px;
                    }

                    .box-list-num {
                        font-size: 16px;
                        font-family: "DINAlternate-Bold", "Arial", sans-serif;
                        font-weight: bold;
                        color: #2d87e6;
                        text-shadow: 0 0 10px rgba(45, 135, 230, 0.6);
                        letter-spacing: 0.5px;
                        transition: all 0.3s ease;
                        background: linear-gradient(135deg, #2d87e6 0%, #4db8ff 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    }
                }
            }

            .bar {
                width: 24px;
                height: 2px;
                background: linear-gradient(90deg, #2d87e6 0%, #4db8ff 50%, #1fa6cd 100%);
                box-shadow: 0 0 6px rgba(45, 135, 230, 0.6);
                border-radius: 1px;
                transition: all 0.3s ease;
            }

            .bottom_bar {
                position: absolute;
                bottom: 8px;
                right: 15px;
            }

            .top_bar {
                position: absolute;
                top: 8px;
                left: 8px;
            }

            .arrow {
                height: 70px;
                width: 35px;
                display: flex;
                justify-content: center;
                align-items: center;
                position: relative;
                z-index: 2;

                img {
                    filter: drop-shadow(0 2px 8px rgba(45, 135, 230, 0.5));
                    transition: all 0.3s ease;
                    opacity: 0.8;
                    width: 16px;
                    height: 16px;
                }
            }

            &:hover .arrow img {
                transform: translateX(4px);
                opacity: 1;
                filter: drop-shadow(0 2px 12px rgba(45, 135, 230, 0.7));
            }
        }
    }

    .chart {
        flex: 1;
        background: linear-gradient(135deg, 
            rgba(8, 28, 52, 0.4) 0%, 
            rgba(5, 20, 38, 0.6) 100%);
        border: 1px solid rgba(45, 85, 135, 0.3);
        border-radius: 12px;
        padding: 20px;
        box-shadow: 
            0 4px 20px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.05);

        .history_chart {
            height: calc(100% - 49px);
        }

        .search_box {
            text-align: right;
            padding-right: 20px;
            margin-bottom: 20px;

            :deep(.el-form-item__label) {
                color: #e6f4ff;
                font-weight: 600;
                font-family: "Alibaba-PuHuiTi";
                letter-spacing: 0.5px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            :deep(.el-date-editor) {
                .el-input__wrapper {
                    background: linear-gradient(135deg, 
                        rgba(15, 35, 65, 0.8) 0%, 
                        rgba(8, 25, 45, 0.9) 100%);
                    border: 1px solid rgba(45, 85, 135, 0.5);
                    border-radius: 8px;
                    box-shadow: 
                        inset 0 1px 4px rgba(0, 0, 0, 0.3),
                        0 2px 8px rgba(0, 0, 0, 0.2);
                    transition: all 0.3s ease;

                    &:hover {
                        border-color: rgba(45, 135, 230, 0.7);
                        box-shadow: 
                            0 0 12px rgba(45, 135, 230, 0.2),
                            inset 0 1px 4px rgba(0, 0, 0, 0.3);
                    }

                    &.is-focus {
                        border-color: #2d87e6;
                        box-shadow: 
                            0 0 15px rgba(45, 135, 230, 0.3),
                            inset 0 1px 0 rgba(255, 255, 255, 0.1);
                    }
                }

                .el-input__inner {
                    color: #e6f4ff;
                    font-weight: 500;

                    &::placeholder {
                        color: rgba(230, 244, 255, 0.4);
                    }
                }

                .el-range-separator {
                    color: rgba(230, 244, 255, 0.6);
                }
            }
        }

        .search_sub {
            margin-left: 20px;
            padding-bottom: 10px;
        }
    }

    .active {
        background: url("@/components/device/img/t_active.png") no-repeat !important;
        background-size: contain !important;
        background-position: center !important;
        filter: drop-shadow(0 0 15px rgba(45, 135, 230, 0.8)) !important;
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .history_content {
        gap: 15px;
        padding: 15px;

        .box-list {
            width: 280px;

            .box-list-item {
                margin-bottom: 12px;

                .content-wrapper {
                    gap: 6px;

                    .header .box-list-title {
                        font-size: 13px;
                    }

                    .item {
                        .label {
                            font-size: 12px;
                        }

                        .box-list-num {
                            font-size: 16px;
                        }
                    }
                }
            }
        }
    }
}
</style>
