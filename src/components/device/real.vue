<template>
    <!-- 实时工况 -->
    <div class="real_content">
        <div class="box-list">
            <el-scrollbar>
                <div v-for="(item, i) in data" :key="i">
                    <div class="box-list-item cursor" 
                         :class="[activeBox == item.id ? 'active-item' : '']"
                         @click="changeBox(item, i)" 
                         v-if="item.typeId == 1 && (item.dataTypeName == 'string' || item.dataTypeName == 'int'
                        || item.dataTypeName == 'float' || item.dataTypeName == 'double')">
                        <div class="icon" :class="[activeBox == item.id ? 'active' : '']"></div>
                        <div class="temperature">
                            <!-- <div class="box-list-title">{{ item.name }}</div> -->
                            <div class="item">
                                <div>{{ item.name }}</div>
                                <div class="box-list-num">
                                    {{ values['n' + i] }}{{ item.unitName }}
                                </div>
                            </div>
                        </div>
                        <div class="arrow">
                            <img src="@/components/device/img/arrow.png" />
                        </div>
                        <div class="bar top_bar"></div>
                        <div class="bar bottom_bar"></div>
                    </div>
                </div>
            </el-scrollbar>
        </div>
        <div class="chart_right">
            <panel :title="realParams.title + '-实时数据'" />
            <realChart :realData="realData" :chartParams="realParams" />
        </div>
    </div>
</template>

<script setup>
import {
    getCookie
} from '@/utils/cookie' // get token from cookie

import dayjs from 'dayjs'
import realChart from '@/components/echarts/realChart.vue'



import Panel from './Panel.vue'
import { useAppStore } from '@/stores/app'
import useSocket from '@/hooks/socket'

const { socketOn,socketEmit } = useSocket();

const props = defineProps({
    deviceId: {
        type: [String, Number],
        required: true
    }
})

socketOn('live', (res) => {
    process(res)
})
socketOn('onVarsChangedCallback', (res) => {
    process(res)
})



const api = inject('$api')
const store = useAppStore()

const data = ref([])
const values = ref({})
const checked = ref(false)
const chartsData = ref({})
const interval = ref(null)
const activeBox = ref('')
const realData = ref({
    time: [],
    data: [],
})
const timer = ref(null)
const realParams = ref({
    unit: '',
    title: '',
})
const swiperOption = ref({
    slidesPerView: 8,
    spaceBetween: 0,
    freeMode: true,
    navigation: {
        nextEl: '.next',
        prevEl: '.prev',
    },
})
const sockets = ref(null)
const unit = ref([])

sockets.value = inject('socket')
onUnmounted(() => {
    if (interval.value) {
        clearInterval(interval.value)
    }
    if (timer.value) {
        clearInterval(timer.value)
    }
})
const projectId = computed(() => {
    return store.projectId || getCookie('gh_projectId')
})

watch(() => props.deviceId, (val) => {
    if (val) {
        getDeviceParams()
    }
})
onMounted(() => {
    getDeviceParams()
})

onUnmounted(() => {
    socketEmit("UnSubscribeBatch",'device')
})

const getDeviceParams = () => {
    api.getDevicesStdById({
        deviceId: props.deviceId,
        projectId: getCookie("gh_projectId"),
        page: 1,
        size: 1
    }).then((res) => {
        data.value = res.data[0].deviceStandards;
        activeBox.value = data.value[0].id
        subscribe(data.value)
    })
}
const subscribe = (dataList) => {
    dataList.forEach((d, i) => {
        values.value = Object.assign({}, values.value, {
            ['n' + i]: 0
        })
        values.value = Object.assign({}, values.value, {
            time: getnewDate(),
        })
        chartsData.value[d.name] = 'n' + i
        if (d.variable) {
            let v = d.variable.split(':')
            let item = {
                id: 'n' + i,
                iosvrKey: v[0],
                chlKey: v[1],
                ctrlKey: v[2],
                varKey: v[3],
                realTime: false,
            }
            socketEmit("Subscribe",JSON.stringify({
                batchDefinitionId: 'device',
                requestItems: [item],
                dev: null
            }))
        }
    })
    getrealData(dataList[0], 0)
}
const getrealData = (item, i) => {
    if (timer.value) {
        window.clearInterval(timer.value)
    }
    realParams.value = {
        title: item.name,
        unit: item.unitName,
    }

    if (realData.value.time.length == 10) {
        realData.value.time.shift()
        realData.value.data.shift()
    }
    let dataValue = parseInt(values.value['n' + i])
    realData.value.time.push(getnewDate())
    realData.value.data.push(dataValue)

    timer.value = setInterval(() => {
        if (realData.value.time.length == 10) {
            realData.value.time.shift()
            realData.value.data.shift()

            let dataValue = parseInt(values.value['n' + i])
            realData.value.time.push(getnewDate())
            realData.value.data.push(dataValue)
        } else {
            let dataValue = parseInt(values.value['n' + i])
            realData.value.time.push(getnewDate())
            realData.value.data.push(dataValue)
        }
    }, 5000)
}
const getnewDate = () => {
    let time = dayjs().format('HH:mm:ss')
    return time
}
const process = (res) => {
    if (res) {
        let responseData = JSON.parse(res)
        if (responseData.batchDefinitionId == 'device') {
            responseData.data.forEach((d) => {
                values.value[d.id] = d.value
                values.value.time = getnewDate()
            })
        }
    }
}
// 点击box
const changeBox = (item, i) => {
    activeBox.value = item.id
    realData.value = {
        time: [],
        data: [],
    }
    getrealData(item, i)
}
</script>

<style lang="scss" scoped>
.real_content {
    display: flex;
    flex-direction: row;
    height: calc(100% - 56px);
    gap: 20px;
    background: linear-gradient(135deg, rgba(5, 26, 48, 0.4) 0%, rgba(2, 15, 30, 0.6) 100%);
    border: 1px solid rgba(45, 85, 135, 0.3);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

    .box-list {
        width: 320px;
        height: calc(100% - 20px);

        :deep(.el-scrollbar) {
            .el-scrollbar__bar {
                &.is-vertical {
                    .el-scrollbar__thumb {
                        background: linear-gradient(135deg, rgba(45, 135, 230, 0.8) 0%, rgba(25, 118, 210, 0.6) 100%);
                        border-radius: 4px;
                        transition: all 0.3s ease;

                        &:hover {
                            background: linear-gradient(135deg, rgba(45, 135, 230, 1) 0%, rgba(25, 118, 210, 0.8) 100%);
                        }
                    }
                }
            }

            .el-scrollbar__track {
                background: rgba(5, 26, 48, 0.5);
                border-radius: 4px;
            }
        }

        .box-list-item {
            background: linear-gradient(135deg, 
                rgba(15, 35, 65, 0.9) 0%, 
                rgba(8, 25, 45, 0.95) 50%,
                rgba(5, 20, 40, 0.9) 100%);
            border: 1px solid rgba(45, 85, 135, 0.4);
            border-left: 4px solid transparent;
            border-radius: 12px;
            margin-bottom: 12px;
            display: flex;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 
                0 4px 20px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, 
                    rgba(45, 135, 230, 0.12) 0%, 
                    rgba(25, 118, 210, 0.08) 50%,
                    transparent 100%);
                opacity: 0;
                transition: all 0.4s ease;
            }

            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, 
                    transparent 0%, 
                    rgba(255, 255, 255, 0.1) 50%, 
                    transparent 100%);
                transition: left 0.6s ease;
            }

            &:hover {
                border-color: rgba(45, 135, 230, 0.7);
                border-left-color: #2d87e6;
                box-shadow: 
                    0 8px 30px rgba(45, 135, 230, 0.25),
                    0 2px 10px rgba(0, 0, 0, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.15);
                transform: translateY(-3px) scale(1.02);

                &::before {
                    opacity: 1;
                }

                &::after {
                    left: 100%;
                }

                .icon {
                    transform: scale(1.05);
                    filter: drop-shadow(0 0 12px rgba(45, 135, 230, 0.6));
                }

                .temperature .item .box-list-num {
                    color: #4db8ff;
                    text-shadow: 0 0 12px rgba(77, 184, 255, 0.6);
                    transform: scale(1.05);
                }
            }

            // 选中状态
            &.active-item {
                border-left-color: #2d87e6;
                background: linear-gradient(135deg, 
                    rgba(45, 135, 230, 0.15) 0%, 
                    rgba(15, 35, 65, 0.9) 30%,
                    rgba(8, 25, 45, 0.95) 70%,
                    rgba(5, 20, 40, 0.9) 100%);
                box-shadow: 
                    0 6px 25px rgba(45, 135, 230, 0.3),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);

                &::before {
                    opacity: 0.8;
                    background: linear-gradient(135deg, 
                        rgba(45, 135, 230, 0.2) 0%, 
                        rgba(25, 118, 210, 0.15) 50%,
                        transparent 100%);
                }

                .bar {
                    box-shadow: 0 0 10px rgba(45, 135, 230, 0.8);
                    background: linear-gradient(90deg, #4db8ff 0%, #2d87e6 100%);
                }
            }

            .icon {
                width: 65px;
                height: 70px;
                background: url("@/components/device/img/t.png") no-repeat;
                background-size: contain;
                background-position: center;
                margin-right: 15px;
                position: relative;
                z-index: 2;
                filter: drop-shadow(0 2px 8px rgba(45, 135, 230, 0.4));
                transition: all 0.3s ease;
            }

            .temperature {
                display: flex;
                flex-direction: column;
                flex: 1;
                position: relative;
                z-index: 2;

                .box-list-title {
                    padding: 14px 8px 0 8px;
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 500;
                    color: rgba(230, 244, 255, 0.9);
                    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
                }

                .item {
                    height: 100%;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 13px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 500;
                    color: #e6f4ff;
                    padding: 12px 10px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);

                    > div:first-child {
                        letter-spacing: 0.5px;
                        line-height: 1.4;
                        max-width: 120px;
                        word-wrap: break-word;
                    }

                    .box-list-num {
                        font-size: 16px;
                        font-family: "DINAlternate-Bold", "Arial", sans-serif;
                        font-weight: bold;
                        color: #2d87e6;
                        margin-left: 12px;
                        text-shadow: 0 0 10px rgba(45, 135, 230, 0.6);
                        min-width: 60px;
                        text-align: right;
                        letter-spacing: 0.5px;
                        transition: all 0.3s ease;
                        background: linear-gradient(135deg, #2d87e6 0%, #4db8ff 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    }
                }
            }

            .bar {
                width: 24px;
                height: 2px;
                background: linear-gradient(90deg, #2d87e6 0%, #4db8ff 50%, #1fa6cd 100%);
                box-shadow: 0 0 6px rgba(45, 135, 230, 0.6);
                border-radius: 1px;
                transition: all 0.3s ease;
            }

            .bottom_bar {
                position: absolute;
                bottom: 8px;
                right: 15px;
            }

            .top_bar {
                position: absolute;
                top: 8px;
                left: 8px;
            }

            .arrow {
                height: 70px;
                width: 35px;
                display: flex;
                justify-content: center;
                align-items: center;
                position: relative;
                z-index: 2;

                img {
                    filter: drop-shadow(0 2px 8px rgba(45, 135, 230, 0.5));
                    transition: all 0.3s ease;
                    opacity: 0.8;
                    width: 16px;
                    height: 16px;
                }
            }

            &:hover .arrow img {
                transform: translateX(4px);
                opacity: 1;
                filter: drop-shadow(0 2px 12px rgba(45, 135, 230, 0.7));
            }
        }
    }

    .active {
        background: url("@/components/device/img/t_active.png") no-repeat !important;
        background-size: contain !important;
        background-position: center !important;
        filter: drop-shadow(0 0 15px rgba(45, 135, 230, 0.8)) !important;
    }

    .chart_right {
        flex: 1;
        background: linear-gradient(135deg, 
            rgba(8, 28, 52, 0.4) 0%, 
            rgba(5, 20, 38, 0.6) 100%);
        border: 1px solid rgba(45, 85, 135, 0.3);
        border-radius: 12px;
        padding: 20px;
        box-shadow: 
            0 4px 20px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.05);

        .real_chart {
            height: calc(100% - 49px);
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .real_content {
        gap: 15px;
        padding: 15px;

        .box-list {
            width: 280px;

            .box-list-item {
                margin-bottom: 12px;

                .temperature .item {
                    padding: 12px 10px;
                    font-size: 13px;

                    .box-list-num {
                        font-size: 18px;
                        min-width: 70px;
                    }
                }
            }
        }
    }
}
</style>