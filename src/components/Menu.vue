<template>
    <div v-if="menuList.length" class="menu">
        <div v-for="item in menuList" :key="item.id" class="item" @click="clickMenu(item)"
             :class="[activeName === item.name ? 'active' : '']">
            <div class="img">
                <img :src="getImgUrl(item.imgName)">
            </div>
            <div class="name alibaba-bold">
                {{ item.name }}
            </div>
        </div>
    </div>
</template>

<script setup>


import {
    getCookie,
    removeToken,
    setCookie
} from '@/utils/cookie'
import { useAppStore } from '@/stores/app'
import { useMitt } from '@/hooks/mitt'

const api = inject('$api')
const router = useRouter()
const store = useAppStore()

const menuList = ref([])
const list = ref([])
const activeName = ref('综合态势')
const secondName = ref('')


const emitter = useMitt()
emitter.miitOn('Switch_System', (data) => {
    console.log(data)
    if (window.cmd != data.selectedFloorId) {
        return;
    }
    if (data.command.toLowerCase() == 'access') {
        data = "door";
    }
    menuList.value.forEach(item => {
        if (item.cmd == data.command) {
            clickMenu(item)
        }
    })
})

onMounted(() => {

    let firstMenuName = sessionStorage.getItem("firstMenuName")
    if (firstMenuName) {
        activeName.value = firstMenuName;
    }

    let secondMenuName = sessionStorage.getItem("secondMenuName")
    if (secondMenuName) {
        secondName.value = secondMenuName;
    }

    getProjectMenu()

});

const getProjectMenu = () => {
    if (getCookie("gh_projectId") != 0) {
        api.getMenuList({
            configFlag: false,
            projectId: getCookie("gh_projectId"),
            menuType: [1, 2],
            isPcMenu: true,
            enable: true,
        }).then((res) => {
            menuList.value = res.data;
            if (!getCookie("funMenus")) {
                clickMenu(res.data[0]);
            }
        })
    }
}

const clickMenu = (item) => {
    if (item) {
        if (item.component && item.component.startsWith("dialog_")) {
            store.SET_DIALOG( {
                name: item.name,
                component: item.component.split('_')[1]
            })
            return;
        }

        //重置二级菜单
        if (item.menuType == 1) {
            list.value = [];
            sessionStorage.removeItem("second")
            emitter.miitEmit('second', {
                list: []
            })
            //一级菜单名称
            sessionStorage.setItem("firstMenuName", item.name)
        }
        //点击的是一级菜单
        if (item.menuType == 1 && item.children && item.children.length > 0) {
            list.value = item.children;

            sessionStorage.setItem("second", JSON.stringify(item.children))
            //记录二级菜单,刷新使用
            emitter.miitEmit('second', {
                list: item.children
            })
        }

        if (item.menuType == 1 && activeName.value !== item.name) {
            activeName.value = item.name
        }


        //一级菜单没有绑定组件，默认显示子集的第一个
        if (item.menuType == 1 && !item.component && item.children.length > 0) {
            item = item.children[0];

            secondName.value = item.name;
            sessionStorage.setItem("secondMenuName", item.name)
            emitter.miitEmit('secondMenuName', {
                name: item.name
            })

        }

        if (item.component) {
            if ((item.menuType == 1 || item.menuType == 2) && item.component.includes("_") && item.component.split("_").length == 2) { //主要是各个子系统第一个
                item.secondComponent = item.component.split("_")[1];
                item.component = item.component.split("_")[0];
            } else if ((item.menuType == 1 || item.menuType == 2) && item.component.includes("_") && item.component.split("_").length == 3) //子系统弹窗
            {
                let components = item.component.split("_");
                item.component = components[0]
                item.secondComponent = components[1]
                item.popName = components[2]
            }
            removeToken("funMenus")
            setCookie('funMenus', JSON.stringify({
                id: item.id,
                name: item.name,
                component: item.component,
                secondComponent: item.secondComponent,
                code: item.code, //菜单编码
                diagramId: item.diagramId,
                popName: item.popName,
                model: item.model,
                showFloor: item.showFloor,
            }))

            store.SET_FUN_MENU(item)
        }

        if (item.cmd) {
            emitter.miitEmit('ue', {
                type: 'menu',
                value: item.cmd.trim()
            })
        }

        router.push({
            path: '/overview'
        })

        emitter.miitEmit('goHome')


    }

}
const getImgUrl = (name) => {
    return new URL(`/src/assets/images/menu/${name}.png`, import.meta.url).href
}




</script>

<style lang="scss" scoped>
.menu {
    position: fixed;
    width: 1000px;
    left: 50%;
    bottom: 5px;
    transform: translateX(-50%);
    z-index: 10;
    height: 60px;
    background: url('@/assets/images/menu/底部导航.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;

    .item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 0 8px;
        cursor: pointer;
        padding: 8px 16px;
        border-radius: 8px;
        transition: all 0.3s ease;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #7ED4BB, #FFFFFF);
            border-radius: 1px;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        &:hover {
            transform: translateY(-3px);
            
            .img img {
                transform: scale(1.1);
            }
            
            .name {
                color: #7ED4BB;
            }
        }

        &.active {
            .img img {
                filter: brightness(1.3) drop-shadow(0 0 8px rgba(126, 212, 187, 0.6));
            }
            
            .name {
                background: linear-gradient(135deg, #FFFFFF 0%, #7ED4BB 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                font-weight: 700;
            }

            &::before {
                width: 80%;
            }
        }

        .img {
            margin-bottom: 4px;

            img {
                width: 24px;
                height: 24px;
                transition: all 0.3s ease;
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            }
        }

        .name {
            font-size: 12px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            transition: all 0.3s ease;
            letter-spacing: 0.5px;
            line-height: 1.2;
        }
    }
}
</style>
