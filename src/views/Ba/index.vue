<template>
    <div class="z100">
        <div class="left">
            <div class="header flex-start">
                <img src="@/assets/images/common/head.png">
                <div> 设备列表</div>
            </div>
            <div class="input">
                <el-input v-model="keyword" @change="searchDevice" prefix-icon="Search" placeholder="按设备名称搜索">
                </el-input>
            </div>
            <div class="device">
                <el-scrollbar v-if="list.length > 0">
                    <div class="list space-between" v-for="item in list" :key="item.id">
                        <div class="center cursor" @click="showDetail(item)">
                            <span class="iconfont " v-if="item.icon" :class="item.icon"></span>
                            <img v-else src="@/assets/images/common/feng.png" />
                            <div class="name">{{ item.name }}</div>
                        </div>
                        <div class="center state" style="margin-right: 15px;">
                            <div :style="{ color: getColor(realData[p.id], p.config) }" v-for="(p, j) in item.state"
                                :key="j">
                                {{ getName(realData[p.id], p.config) }}</div>
                        </div>
                        <div class="position cursor" @click="zoomToPosition(item)">
                            <img src="@/assets/images/common/position.png" />
                        </div>
                    </div>
                </el-scrollbar>
                <noData v-else></noData>
            </div>

            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>

        </div>

        <diagram v-model:show="showDevice" :name="deviceDetail.name">
            <div class="detail">
                <div class="tabs">
                    <div v-for="item in tabs" :class="item.component == tabComponent ? 'active' : ''"
                        class="center tab cursor" :key="item.component" @click="changeTab(item)">
                        <span class="name">{{ item.name }}</span>
                    </div>
                </div>

                <component :is="tabComponent" :deviceId="deviceDetail.id"></component>
            </div>
        </diagram>

        <!-- 中间查询记录 -->
        <pop :show="activeMenus.popName ? true : false" :title="activeMenus.name || ''">
            <Transition name="fade" mode="out-in">
                <component :is="activeMenus.popName"></component>
            </Transition>
        </pop>

        <!-- 右侧列表 -->
        <Transition name="fade" mode="out-in" appear>
            <component :is="activeMenus.secondComponent"></component>
        </Transition>

    </div>
</template>

<script setup>
import { ref, computed, watch, inject, onMounted, onUnmounted, nextTick } from 'vue'
import { getCookie } from "@/utils/cookie"
import calc from '@/utils/eval'
import diagram from '@/components/diagram/index.vue'
import pop from '@/components/pop/index.vue'
import noData from '@/components/noData.vue'
import { useAppStore } from '@/stores/app'
import useSocket from "@/hooks/socket"

// 定义组件名称
defineOptions({
    name: "common"
})

// 获取依赖
const { socketEmit, socketOn } = useSocket()
const api = inject('$api')
const store = useAppStore()
const emitter = inject('mitt')

// 响应式数据
const keyword = ref("")
const size = ref(10)
const page = ref(1)
const total = ref(0)
const showDevice = ref(false)
const list = ref([]) // 设备列表
const realData = ref({}) // 订阅返回的实时数据
const tabComponent = ref("device")
const deviceDetail = ref({
    name: null,
    id: null,
})

const tabs = ref([
    {
        name: '设备信息',
        component: 'device'
    },
    {
        name: '实时数据',
        component: 'real'
    },
    {
        name: '历史工况',
        component: 'history'
    },
    {
        name: '运行记录',
        component: 'run'
    },
    {
        name: '报警记录',
        component: 'DeviceAlarmLog'
    },
    {
        name: '维保记录',
        component: 'work'
    },
    {
        name: '维修记录',
        component: 'repair'
    },
    {
        name: '健康度',
        component: 'health'
    },
    {
        name: '设备面板',
        component: 'cmd'
    },
])

// 计算属性
const activeMenus = computed(() => {
    let menu = getCookie("funMenus")
    return store.funMenus ?
        store.funMenus :
        menu ?
            JSON.parse(menu) :
            ""
})

const areaId = computed(() => {
    return store.area
})

// 监听器
watch(areaId, (val) => {
    getDeviceList()
})

watch(activeMenus, (val) => {
    if (!val.popName) { // 中间记录不需要加载设备
        getDeviceList()
    }
})

// 方法
const deviceStd = (data) => {
    let ws = []
    list.value = []
    data.forEach((d, i) => {
        if (d.deviceStandards) {
            let data = {}
            data.name = d.name
            data.icon = d.icon
            data.id = d.id
            data.code = d.code
            data.state = []
            //设备指标
            d.deviceStandards.filter(ds => ds.showList).forEach((s, j) => {
                let id = d.productId + "_" + d.id + '_' + s.identifier;
                //状态输入
                if (s.dataTypeName == 'bool') {
                // if (s.typeId == 1 && s.dataTypeName == 'bool') {
                    data.state.push({
                        name: s.name,
                        id: id,
                        config: s.config ? JSON.parse(s.config) : null
                    })
                    let item = {
                        productId: d.productId,
                        deviceId: d.id,
                        identifier: s.identifier,
                    }
                    realData.value = Object.assign({}, realData.value, {
                        [id]: 0,
                    })
                    ws.push(item)
                }
            })
            list.value.push(data)
        }
    })
    if (ws.length > 0) {
        nextTick(() => {
            socketEmit("Subscribe",JSON.stringify({ batchDefinitionId: "ba", requestItems: ws,dev:null }));
        })
    }
}

const getDeviceList = async () => {
    socketEmit("UnSubscribeBatch", "ba")
    let id = null
    if (areaId.value && areaId.value instanceof Array) {
        id = areaId.value
    } else if (areaId.value && areaId.value.id == -1) {
        id = null
    } else if (areaId.value && areaId.value.id != -1) {
        id = areaId.value.id
    }
   
    let {
        data,
        total: totalCount
    } = await api.getDevicesStd({
        areaId: id,
        menuId: activeMenus.value ? activeMenus.value.id : "",
        keyword: keyword.value,
        size: size.value,
        page: page.value,
        projectId: getCookie("gh_projectId")
    })
    total.value = totalCount
    deviceStd(data)
}

const handleCurrentChange = (currentPage) => {
    page.value = currentPage
    getDeviceList()
}

const searchDevice = () => {
    page.value = 1
    getDeviceList()
}

const getName = (value, config) => {
    let name = ""
    if (config && config.length && config.length > 0) {
        config.forEach(c => {
            if (calc(value, c.factor, c.value)) {
                name = c.text
            }
        })
    }
    return name
}

const getColor = (value, config) => {
    let color = ""
    if (config && config.length && config.length > 0) {
        config.forEach(c => {
            if (calc(value, c.factor, c.value)) {
                color = c.color
            }
        })
    }
    return color
}

const subscribeData = (res) => {
    if (res) {
        let data = JSON.parse(res)
        if (data.batchDefinitionId == "ba") {
            data.data.forEach((d) => {
                realData.value[d.id] = Number(d.value)
            })
        }
    }
}

const showDetail = (item) => {
    showDevice.value = true
    deviceDetail.value = item
}

const changeTab = (item) => {
    tabComponent.value = item.component
}

const zoomToPosition = (item) => {
    if (item.code) {
        emitter.emit('ue', {
            type: 'position',
            token: item.code.startsWith("energy_") ? item.name : item.code,
        })
    }
}

// 生命周期钩子
onMounted(() => {
    getDeviceList()

    socketOn("live", subscribeData)
    socketOn("onVarsChangedCallback", subscribeData)

})

onUnmounted(() => {
    socketEmit("UnSubscribeBatch", "ba")
})
</script>

<style lang="scss" scoped>
.z100 {
    .left {
        .header {
            background: linear-gradient(90deg, rgba(45, 85, 135, 0.3) 0%, transparent 100%);
            border-left: 3px solid #2d87e6;
            padding: 10px 12px;
            margin-bottom: 16px;
            border-radius: 4px;

            img {
                width: 20px;
                height: 20px;
                margin-right: 8px;
                filter: drop-shadow(0 0 6px rgba(45, 135, 230, 0.4));
            }

            div {
                font-size: 16px;
                font-weight: 500;
                color: #e6f4ff;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
            }
        }

        .input {
            margin-bottom: 16px;

            :deep(.el-input) {
                .el-input__wrapper {
                    background: linear-gradient(135deg, rgba(5, 26, 48, 0.7) 0%, rgba(2, 15, 30, 0.9) 100%);
                    border: 1px solid rgba(45, 85, 135, 0.5);
                    border-radius: 6px;
                    box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.3);
                    transition: all 0.3s ease;

                    &:hover {
                        border-color: rgba(45, 135, 230, 0.7);
                        box-shadow: 0 0 8px rgba(45, 135, 230, 0.2);
                    }

                    &.is-focus {
                        border-color: #2d87e6;
                        box-shadow: 0 0 12px rgba(45, 135, 230, 0.3);
                    }
                }

                .el-input__inner {
                    color: #e6f4ff;
                    font-size: 14px;

                    &::placeholder {
                        color: rgba(230, 244, 255, 0.5);
                    }
                }

                .el-input__prefix-inner {
                    color: rgba(45, 135, 230, 0.8);
                }
            }
        }

        .device {
            :deep(.el-scrollbar) {
                .el-scrollbar__bar {
                    &.is-vertical {
                        .el-scrollbar__thumb {
                            background: linear-gradient(135deg, rgba(45, 135, 230, 0.7) 0%, rgba(25, 118, 210, 0.5) 100%);
                            border-radius: 3px;

                            &:hover {
                                background: linear-gradient(135deg, rgba(45, 135, 230, 0.9) 0%, rgba(25, 118, 210, 0.7) 100%);
                            }
                        }
                    }
                }

                .el-scrollbar__track {
                    background: rgba(5, 26, 48, 0.4);
                    border-radius: 3px;
                }
            }

            :deep(.no_data) {
          
                border: 1px solid rgba(45, 85, 135, 0.3);
                border-radius: 6px;
                padding: 30px 16px;
                margin: 16px 0;
                color: rgba(230, 244, 255, 0.6);
                font-size: 14px;

                .icon img {
                    filter: drop-shadow(0 0 6px rgba(45, 135, 230, 0.4));
                    opacity: 0.8;
                }
            }

            .list {
                background: linear-gradient(135deg, rgba(5, 26, 48, 0.5) 0%, rgba(2, 15, 30, 0.7) 100%);
                border: 1px solid rgba(45, 85, 135, 0.4);
                border-left: 3px solid #2d87e6;
                border-radius: 6px;
                margin-bottom: 10px;
                padding: 10px 12px;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(135deg, rgba(45, 135, 230, 0.08) 0%, transparent 50%);
                    opacity: 0;
                    transition: opacity 0.3s ease;
                }

                &:hover {
                    border-color: rgba(45, 135, 230, 0.6);
                    box-shadow: 0 2px 12px rgba(45, 135, 230, 0.2);
                    transform: translateY(-1px);

                    &::before {
                        opacity: 1;
                    }

                    .name {
                        color: #5ba3f5;
                    }
                }

                .name {
                    font-size: 14px;
                    font-weight: 500;
                    color: #e6f4ff;
                    transition: color 0.3s ease;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
                }

                .iconfont {
                    font-size: 22px;
                    color: #7bb8f7;
                    margin: 0 10px;
                    filter: drop-shadow(0 0 6px rgba(123, 184, 247, 0.4));
                    transition: color 0.3s ease;
                }

                img {
                    width: 22px;
                    height: 22px;
                    margin: 0 10px;
                    filter: drop-shadow(0 0 6px rgba(123, 184, 247, 0.4));
                }

                .state {
                    div {
                        font-size: 12px;
                        font-weight: 500;
                        margin-right: 12px;
                        padding: 3px 6px;
                        border-radius: 3px;
                        background: rgba(2, 15, 30, 0.6);
                        border: 1px solid rgba(45, 85, 135, 0.3);
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
                        transition: all 0.3s ease;

                        &:hover {
                            background: rgba(2, 15, 30, 0.8);
                            border-color: rgba(45, 85, 135, 0.5);
                        }
                    }
                }

                .position {
                    cursor: pointer;
                    padding: 6px;
                    border-radius: 4px;
                    background: linear-gradient(135deg, rgba(45, 135, 230, 0.15) 0%, rgba(45, 135, 230, 0.08) 100%);
                    border: 1px solid rgba(45, 135, 230, 0.3);
                    transition: all 0.3s ease;

                    &:hover {
                        background: linear-gradient(135deg, rgba(45, 135, 230, 0.25) 0%, rgba(45, 135, 230, 0.15) 100%);
                        border-color: rgba(45, 135, 230, 0.5);
                        box-shadow: 0 0 10px rgba(45, 135, 230, 0.3);
                        transform: scale(1.05);
                    }

                    img {
                        width: 18px;
                        height: 18px;
                        margin: 0;
                        filter: drop-shadow(0 0 6px rgba(45, 135, 230, 0.6));
                    }
                }
            }
        }

        .page {
            // margin-top: 16px;
            // padding: 12px 0;
            border-top: 1px solid rgba(45, 85, 135, 0.4);

            :deep(.el-pagination) {
                .el-pager li {
                    background: linear-gradient(135deg, rgba(5, 26, 48, 0.7) 0%, rgba(2, 15, 30, 0.9) 100%);
                    border: 1px solid rgba(45, 85, 135, 0.4);
                    color: #e6f4ff;
                    border-radius: 4px;
                    margin: 0 2px;
                    transition: all 0.3s ease;

                    &:hover {
                        border-color: rgba(45, 135, 230, 0.7);
                        box-shadow: 0 0 8px rgba(45, 135, 230, 0.3);
                    }

                    &.is-active {
                        background: linear-gradient(135deg, #2d87e6 0%, #1976d2 100%);
                        color: #ffffff;
                        border-color: #2d87e6;
                        box-shadow: 0 0 10px rgba(45, 135, 230, 0.5);
                    }
                }

                .btn-prev, .btn-next {
                    background: linear-gradient(135deg, rgba(5, 26, 48, 0.7) 0%, rgba(2, 15, 30, 0.9) 100%);
                    border: 1px solid rgba(45, 85, 135, 0.4);
                    color: #e6f4ff;
                    border-radius: 4px;
                    transition: all 0.3s ease;

                    &:hover {
                        border-color: rgba(45, 135, 230, 0.7);
                        box-shadow: 0 0 8px rgba(45, 135, 230, 0.3);
                    }
                }
            }
        }
    }
}

.detail {
    display: flex;
    flex-direction: column;
    height: 100%;

    &>div:nth-child(2) {
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .tabs {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        padding: 8px;
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.5) 0%, rgba(2, 15, 30, 0.7) 100%);
        border: 1px solid rgba(45, 85, 135, 0.4);
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

        .tab {
            background: linear-gradient(135deg, rgba(5, 26, 48, 0.6) 0%, rgba(2, 15, 30, 0.8) 100%);
            border: 1px solid rgba(45, 85, 135, 0.4);
            width: 104px;
            height: 38px;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;

            &:hover {
                border-color: rgba(45, 135, 230, 0.6);
                box-shadow: 0 2px 8px rgba(45, 135, 230, 0.2);
                transform: translateY(-1px);

                .name {
                    color: #7bb8f7;
                }
            }

            .name {
                font-size: 14px;
                font-weight: 400;
                color: #e6f4ff;
                transition: color 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
            }
        }

        .active {
            background: linear-gradient(135deg, 
                rgba(45, 85, 135, 0.8) 0%, 
                rgba(25, 65, 110, 0.9) 50%, 
                rgba(15, 45, 85, 0.8) 100%);
            border: 2px solid rgba(45, 85, 135, 0.6);
            box-shadow: 
                0 0 15px rgba(45, 85, 135, 0.4),
                0 3px 12px rgba(25, 65, 110, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, 
                    rgba(45, 85, 135, 0.2) 0%, 
                    transparent 50%, 
                    rgba(25, 65, 110, 0.15) 100%);
                border-radius: 6px;
                opacity: 0.6;
            }

            &:hover {
                border-color: rgba(45, 85, 135, 0.8);
                box-shadow: 
                    0 0 20px rgba(45, 85, 135, 0.5),
                    0 4px 16px rgba(25, 65, 110, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.15);
                transform: translateY(-1px);
            }

            .name {
                color: #e6f4ff;
                font-weight: 600;
                text-shadow: 
                    0 1px 3px rgba(0, 0, 0, 0.7),
                    0 0 6px rgba(45, 85, 135, 0.3);
                position: relative;
                z-index: 1;
            }
        }
    }
}

// 添加动画关键帧
@keyframes tabGlow {
    0% {
        box-shadow:
            0 0 15px rgba(45, 85, 135, 0.4),
            0 3px 12px rgba(25, 65, 110, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        border-color: rgba(45, 85, 135, 0.6);
    }
    50% {
        box-shadow:
            0 0 22px rgba(45, 85, 135, 0.6),
            0 5px 18px rgba(25, 65, 110, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.15);
        border-color: rgba(45, 85, 135, 0.8);
    }
    100% {
        box-shadow:
            0 0 15px rgba(45, 85, 135, 0.4),
            0 3px 12px rgba(25, 65, 110, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        border-color: rgba(45, 85, 135, 0.6);
    }
}

@keyframes tabSlideIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

// 为每个tab添加延迟动画
.detail .tabs .tab {
    animation: tabSlideIn 0.6s ease-out;

    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.15s; }
    &:nth-child(3) { animation-delay: 0.2s; }
    &:nth-child(4) { animation-delay: 0.25s; }
    &:nth-child(5) { animation-delay: 0.3s; }
    &:nth-child(6) { animation-delay: 0.35s; }
    &:nth-child(7) { animation-delay: 0.4s; }
    &:nth-child(8) { animation-delay: 0.45s; }
    &:nth-child(9) { animation-delay: 0.5s; }
}

// 激活状态的呼吸动画
.detail .tabs .tab.active {
    animation: tabSlideIn 0.6s ease-out, tabGlow 2s ease-in-out infinite;
}
</style>
