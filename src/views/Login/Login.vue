<template>
    <div class="login">
        <login-heade>
            <!-- 登录表单 -->
            <div class="form-container" v-if="!register">
                <div class="form-header">
                    <div class="logo-section">
                        <div class="logo-icon">
                            <i class="iconfont iconprofile"></i>
                        </div>
                        <h2 class="form-title">用户登录</h2>
                        <p class="form-subtitle">欢迎使用智慧建筑运行管理平台</p>
                    </div>
                </div>

                <el-form class="loginForm" :model="form" ref="ruleForm" :rules="rules" label-width="0">
                    <div class="form-group">
                        <el-form-item prop="username">
                            <div class="input-wrapper">
                                <i class="input-icon iconfont iconprofile"></i>
                                <el-input
                                    type="text"
                                    v-model="form.username"
                                    placeholder="请输入用户名"
                                    class="custom-input"
                                ></el-input>
                            </div>
                        </el-form-item>
                    </div>

                    <div class="form-group">
                        <el-form-item prop="password">
                            <div class="input-wrapper">
                                <i class="input-icon iconfont iconmima"></i>
                                <el-input
                                    type="password"
                                    v-model="form.password"
                                    placeholder="请输入密码"
                                    class="custom-input"
                                    show-password
                                ></el-input>
                            </div>
                        </el-form-item>
                    </div>

                    <div class="form-group" v-if="form.username.toLowerCase() === 'demo'">
                        <el-form-item prop="code">
                            <div class="input-wrapper">
                                <i class="input-icon iconfont iconkaiguan"></i>
                                <el-input
                                    type="text"
                                    v-model="form.code"
                                    placeholder="请输入邀请码"
                                    class="custom-input"
                                ></el-input>
                            </div>
                        </el-form-item>
                    </div>

                    <div class="form-actions">
                        <el-button
                            :loading="isLogin"
                            @click="handleSubmit('ruleForm')"
                            type="primary"
                            class="login-button"
                            size="large"
                        >
                            <span v-if="!isLogin">立即登录</span>
                            <span v-else>登录中...</span>
                        </el-button>
                    </div>
                </el-form>
            </div>

            <!-- 注册表单 -->
            <div class="form-container" v-if="register">
                <div class="form-header">
                    <div class="close-btn" @click="back">
                        <i class="iconfont iconchahao"></i>
                    </div>
                    <div class="logo-section">
                        <div class="logo-icon">
                            <i class="iconfont iconxinzeng"></i>
                        </div>
                        <h2 class="form-title">用户注册</h2>
                        <p class="form-subtitle">创建您的账户</p>
                    </div>
                </div>

                <el-form class="loginForm" :model="userForm" ref="registerForm" :rules="regRules" label-width="0">
                    <div class="form-group">
                        <el-form-item prop="name">
                            <div class="input-wrapper">
                                <i class="input-icon iconfont iconprofile"></i>
                                <el-input type="text" v-model="userForm.name" placeholder="请输入用户名" class="custom-input"></el-input>
                            </div>
                        </el-form-item>
                    </div>

                    <div class="form-group">
                        <el-form-item prop="userName">
                            <div class="input-wrapper">
                                <i class="input-icon iconfont iconwode"></i>
                                <el-input type="text" v-model="userForm.userName" placeholder="请输入登录名" class="custom-input"></el-input>
                            </div>
                        </el-form-item>
                    </div>

                    <div class="form-group">
                        <el-form-item prop="phone">
                            <div class="input-wrapper">
                                <i class="input-icon iconfont iconxiaoxi"></i>
                                <el-input v-model.number="userForm.phone" placeholder="请输入手机号" class="custom-input"></el-input>
                            </div>
                        </el-form-item>
                    </div>

                    <div class="form-group">
                        <el-form-item prop="email">
                            <div class="input-wrapper">
                                <i class="input-icon iconfont iconyoujian"></i>
                                <el-input v-model="userForm.email" placeholder="请输入邮箱" class="custom-input"></el-input>
                            </div>
                        </el-form-item>
                    </div>

                    <div class="form-actions">
                        <el-button
                            :loading="isLogin"
                            @click="handleRegister('registerForm')"
                            type="primary"
                            class="login-button"
                            size="large"
                        >
                            <span v-if="!isLogin">立即注册</span>
                            <span v-else>注册中...</span>
                        </el-button>
                    </div>
                </el-form>
            </div>
        </login-heade>
        <Verify @success="login" @close="close" :captchaType="'blockPuzzle'"
            :imgSize="{ width: '400px', height: '200px' }" ref="verify">
        </Verify>
    </div>
</template>

<script setup>
import LoginHeade from './LoginHeade.vue';
import Verify from '@/components/verifition/Verify.vue';
import {
    encode
} from 'js-base64';
import {
    getCookie,setCookie
} from '@/utils/cookie';
import {
    ElMessage
} from 'element-plus';
import { useAppStore } from '@/stores/app';
import useSocket from '@/hooks/socket';

const api = inject('$api')
const store = useAppStore();
const router = useRouter();
const registerForm = ref();
const register = ref(false);
const verify = ref();
const ruleForm = ref();
const form = ref({
    username: '',
    password: '',
    code: '',
    params: '',
    autoLogin: true
})
const userForm = ref({
    userName: '',
    name: '',
    phone: '',
    email: ''
})


const isLogin = ref(false);

const rules = ref({

    username: [{
        required: true,
        message: '请输入账号',
        trigger: 'blur'
    }],
    password: [{
        required: true,
        message: '请输入密码',
        trigger: 'blur'
    }],
    code: [{
        required: true,
        message: '请输入邀请码',
        trigger: 'blur'
    }],


})

const regRules = ref({
    userName: [{
        required: true,
        message: '请输入登录名',
        trigger: 'blur'
    }],
    name: [{
        required: true,
        message: '请输入用户名',
        trigger: 'blur'
    }],
    password: [{
        required: true,
        message: '请输入密码',
        trigger: 'blur'
    }],
    email: [{
        required: true,
        message: '请输入邮箱',
        trigger: 'blur'
    }, {
        validator: (rule, value, callback) => {
            const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
            if (reg.test(value)) {
                callback();
            } else {
                callback(new Error('请输入正确的邮箱'));
            }
        },
        trigger: 'blur'
    }],
    phone: [{
        required: true,
        message: '请输入手机号',
        trigger: 'blur'
    }, {
        validator: (rule, value, callback) => {
            const reg = /^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/;
            if (reg.test(value)) {
                callback();
            } else {
                callback(new Error("请输入正确的手机号码"));
            }
        },
        trigger: 'blur'
    }],
})



const { socketEmit } = useSocket()

const handleSubmit = (formName) => {
    ruleForm.value.validate((valid) => {
        if (valid) {
            //login();
            isLogin.value = true;
            verify.value.show();
        }
    });
};
const clickRegister = () => {
    register.value = true
}
// 注册
const handleRegister = (formName) => {
    registerForm.value.validate((valid) => {
        if (valid) {
            api.register(userForm.value).then(res => {
                if (res.success) {
                    register.value = false;
                    ElMessage({
                        message: '注册成功',
                        type: 'success'
                    });
                    router.push({
                        path: '/login'
                    })
                }
            })
        } else {
            return false
        }
    })
}
// 登录
const login = async (params) => {
    try {


        const { data } = await api.login({
            username: encode(form.value.username),
            password: encode(form.value.password),
            code: form.value.code,
            params: { captchaVerification: null }
        });

        store.SET_TOKEN(data.access_token);
        setCookie("gh_token", data.access_token);
        setCookie("gh_id", data.userId);
        setCookie("gh_name", data.name);
        setCookie("_refreshToken", data.refresh_token);

        const res = await api.getDefaultProject()
        setCookie("gh_projectId", res.data);
        store.SET_PROJECT_ID(res.data)

        router.push({
            path: '/'
        })
        socketEmit('SetProject', getCookie('gh_projectId'));
        socketEmit('SetUserId', getCookie('gh_id'));
    } catch (e) {
        isLogin.value = false;
    }
};
const back = () => {
    register.value = false
}
const close = () => {
    isLogin.value = false;
}

</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.login {
    position: relative;
    width: 100%;
    height: 100%;
    background: url("@/assets/images/login.jpg");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    overflow: hidden;

    // 添加动态背景效果
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
            135deg,
            rgba(9, 24, 34, 0.4) 0%,
            rgba(16, 52, 87, 0.3) 50%,
            rgba(24, 64, 104, 0.2) 100%
        );
        backdrop-filter: blur(1px);
        z-index: 1;
    }

    .form-container {
        position: relative;
        z-index: 2;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        animation: slideInUp 0.8s ease-out;
    }

    .form-header {
        position: relative;
        margin-bottom: 32px;

        .close-btn {
            position: absolute;
            top: -10px;
            right: 0;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, rgba(255, 71, 87, 0.2) 0%, rgba(255, 71, 87, 0.1) 100%);
            border: 1px solid rgba(255, 71, 87, 0.3);
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(4px);

            &:hover {
                background: linear-gradient(135deg, rgba(255, 71, 87, 0.3) 0%, rgba(255, 71, 87, 0.2) 100%);
                border-color: rgba(255, 71, 87, 0.5);
                transform: scale(1.1);
                box-shadow: 0 4px 12px rgba(255, 71, 87, 0.2);
            }

            i {
                color: #ff4757;
                font-size: 16px;
            }
        }

        .logo-section {
            text-align: center;

            .logo-icon {
                width: 64px;
                height: 64px;
                margin: 0 auto 16px;
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.1) 100%);
                border: 2px solid rgba(61, 233, 250, 0.3);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                backdrop-filter: blur(8px);
                box-shadow:
                    0 8px 32px rgba(61, 233, 250, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1);
                animation: pulse 2s infinite;

                i {
                    font-size: 28px;
                    color: #3de9fa;
                    text-shadow: 0 0 10px rgba(61, 233, 250, 0.5);
                }
            }

            .form-title {
                font-size: 28px;
                font-weight: 600;
                color: #E6F4FF;
                margin: 0 0 8px 0;
                text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
                font-family: 'DOUYU', 'PingFang SC', sans-serif;
            }

            .form-subtitle {
                font-size: 14px;
                color: rgba(230, 244, 255, 0.7);
                margin: 0;
                text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
            }
        }
    }

    .loginForm {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 20px;

        .form-group {
            margin-bottom: 0;

            .input-wrapper {
                position: relative;
                display: flex;
                align-items: center;

                .input-icon {
                    position: absolute;
                    left: 16px;
                    z-index: 3;
                    font-size: 18px;
                    color: rgba(61, 233, 250, 0.7);
                    transition: all 0.3s ease;
                }

                :deep(.el-input) {
                    .el-input__wrapper {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%);
                        border: 1px solid rgba(61, 233, 250, 0.3);
                        border-radius: 12px;
                        box-shadow:
                            inset 0 2px 8px rgba(0, 0, 0, 0.2),
                            0 1px 0 rgba(255, 255, 255, 0.05);
                        backdrop-filter: blur(8px);
                        transition: all 0.3s ease;
                        height: 52px;
                        padding-left: 48px;

                        &:hover {
                            border-color: rgba(61, 233, 250, 0.5);
                            box-shadow:
                                inset 0 2px 8px rgba(0, 0, 0, 0.2),
                                0 0 16px rgba(61, 233, 250, 0.1);
                        }

                        &.is-focus {
                            border-color: #3de9fa;
                            box-shadow:
                                inset 0 2px 8px rgba(0, 0, 0, 0.2),
                                0 0 20px rgba(61, 233, 250, 0.2);
                        }
                    }

                    .el-input__inner {
                        color: #E6F4FF;
                        font-size: 15px;
                        font-weight: 400;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

                        &::placeholder {
                            color: rgba(230, 244, 255, 0.5);
                        }
                    }
                }

                &:focus-within .input-icon {
                    color: #3de9fa;
                    transform: scale(1.1);
                }
            }
        }

        .form-actions {
            margin-top: 12px;

            .login-button {
                width: 100%;
                height: 52px;
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.9) 100%);
                border: none;
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: 600;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow:
                    0 4px 16px rgba(61, 233, 250, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                    transition: left 0.6s ease;
                }

                &:hover {
                    background: linear-gradient(135deg, rgba(61, 233, 250, 0.9) 0%, rgba(17, 150, 252, 1) 100%);
                    box-shadow:
                        0 6px 24px rgba(61, 233, 250, 0.4),
                        inset 0 1px 0 rgba(255, 255, 255, 0.3);
                    transform: translateY(-2px);

                    &::before {
                        left: 100%;
                    }
                }

                &:active {
                    transform: translateY(0);
                    box-shadow:
                        0 2px 8px rgba(61, 233, 250, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
                }

                &.is-loading {
                    background: linear-gradient(135deg, rgba(61, 233, 250, 0.6) 0%, rgba(17, 150, 252, 0.7) 100%);
                    cursor: not-allowed;
                    transform: none;

                    &:hover {
                        transform: none;
                    }
                }
            }
        }
    }
}

// 动画效果
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        box-shadow:
            0 8px 32px rgba(61, 233, 250, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    50% {
        box-shadow:
            0 8px 32px rgba(61, 233, 250, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
}

// 响应式设计
@media (max-width: 768px) {
    .login {
        .form-container {
            padding: 20px;
        }

        .form-header {
            .logo-section {
                .logo-icon {
                    width: 56px;
                    height: 56px;

                    i {
                        font-size: 24px;
                    }
                }

                .form-title {
                    font-size: 24px;
                }
            }
        }
    }
}
</style>
