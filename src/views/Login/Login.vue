<template>
    <div class="login">
        <login-heade>
            <el-form class="loginForm" :model="form" ref="ruleForm" :rules="rules" label-width="110px" v-if="!register">
                <div class="title">
                    <h3>用户登录</h3>
                </div>
                <el-form-item label="用户名：" prop="username">
                    <el-input type="text" v-model="form.username" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item label="密　码：" prop="password">
                    <el-input type="password" v-model="form.password" placeholder="请输入密码"></el-input>
                </el-form-item>
                <el-form-item label="邀请码：" prop="code" v-if="form.username.toLowerCase() === 'demo'">
                    <el-input type="text" v-model="form.code" placeholder="请输入邀请码"></el-input>
                </el-form-item>
                <el-form-item label-width="0">
                    <el-button :loading="isLogin" @click="handleSubmit('ruleForm')" type="primary" class="login_btn">
                        立即登录
                    </el-button>
                </el-form-item>
            </el-form>

            <!-- 用户注册 -->
            <el-form class="loginForm" :model="userForm" ref="registerForm" :rules="regRules" label-width="100px" v-if="register">
                <i class="el-icon-close closeBtn" @click="back"></i>
                <div class="title">
                    <h3>用户注册</h3>
                </div>
                <!-- 用户名 -->
                <el-form-item label="用户名：" prop="name">
                    <el-input type="text" v-model="userForm.name" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <!-- 登录名 -->
                <el-form-item label="登录名：" prop="userName">
                    <el-input type="text" v-model="userForm.userName" placeholder="请输入登录名"></el-input>
                </el-form-item>
                <el-form-item label="手机号：" prop="phone">
                    <el-input v-model.number="userForm.phone" placeholder="请输入手机号"></el-input>
                </el-form-item>
                <el-form-item label="邮　箱：" prop="email">
                    <el-input v-model="userForm.email" placeholder="请输入邮箱"></el-input>
                </el-form-item>
                <!-- 登录button -->
                <el-form-item label-width="0">
                    <el-button :loading="isLogin" @click="handleRegister('registerForm')" type="primary" class="login_btn">
                        立即注册
                    </el-button>
                </el-form-item>
            </el-form>
        </login-heade>
        <Verify @success="login" @close="close" :captchaType="'blockPuzzle'"
            :imgSize="{ width: '400px', height: '200px' }" ref="verify">
        </Verify>
    </div>
</template>

<script setup>
import { ref, inject } from 'vue';
import { useRouter } from 'vue-router';
import LoginHeade from './LoginHeade.vue';
import Verify from '@/components/verifition/Verify.vue';
import {
    encode
} from 'js-base64';
import {
    getCookie,setCookie
} from '@/utils/cookie';
import {
    ElMessage
} from 'element-plus';
import { useAppStore } from '@/stores/app';
import useSocket from '@/hooks/socket';

const api = inject('$api')
const store = useAppStore();
const router = useRouter();
const registerForm = ref();
const register = ref(false);
const verify = ref();
const ruleForm = ref();
const form = ref({
    username: '',
    password: '',
    code: '',
    params: '',
    autoLogin: true
})
const userForm = ref({
    userName: '',
    name: '',
    phone: '',
    email: ''
})


const isLogin = ref(false);

const rules = ref({

    username: [{
        required: true,
        message: '请输入账号',
        trigger: 'blur'
    }],
    password: [{
        required: true,
        message: '请输入密码',
        trigger: 'blur'
    }],
    code: [{
        required: true,
        message: '请输入邀请码',
        trigger: 'blur'
    }],


})

const regRules = ref({
    userName: [{
        required: true,
        message: '请输入登录名',
        trigger: 'blur'
    }],
    name: [{
        required: true,
        message: '请输入用户名',
        trigger: 'blur'
    }],
    password: [{
        required: true,
        message: '请输入密码',
        trigger: 'blur'
    }],
    email: [{
        required: true,
        message: '请输入邮箱',
        trigger: 'blur'
    }, {
        validator: (rule, value, callback) => {
            const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
            if (reg.test(value)) {
                callback();
            } else {
                callback(new Error('请输入正确的邮箱'));
            }
        },
        trigger: 'blur'
    }],
    phone: [{
        required: true,
        message: '请输入手机号',
        trigger: 'blur'
    }, {
        validator: (rule, value, callback) => {
            const reg = /^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/;
            if (reg.test(value)) {
                callback();
            } else {
                callback(new Error("请输入正确的手机号码"));
            }
        },
        trigger: 'blur'
    }],
})



const { socketEmit } = useSocket()

const handleSubmit = (formName) => {
    ruleForm.value.validate((valid) => {
        if (valid) {
            //login();
            isLogin.value = true;
            verify.value.show();
        }
    });
};
const clickRegister = () => {
    register.value = true
}
// 注册
const handleRegister = (formName) => {
    registerForm.value.validate((valid) => {
        if (valid) {
            api.register(userForm.value).then(res => {
                if (res.success) {
                    register.value = false;
                    ElMessage({
                        message: '注册成功',
                        type: 'success'
                    });
                    router.push({
                        path: '/login'
                    })
                }
            })
        } else {
            return false
        }
    })
}
// 登录
const login = async (params) => {
    try {


        const { data } = await api.login({
            username: encode(form.value.username),
            password: encode(form.value.password),
            code: form.value.code,
            params: { captchaVerification: null }
        });

        store.SET_TOKEN(data.access_token);
        setCookie("gh_token", data.access_token);
        setCookie("gh_id", data.userId);
        setCookie("gh_name", data.name);
        setCookie("_refreshToken", data.refresh_token);

        const res = await api.getDefaultProject()
        setCookie("gh_projectId", res.data);
        store.SET_PROJECT_ID(res.data)

        router.push({
            path: '/'
        })
        socketEmit('SetProject', getCookie('gh_projectId'));
        socketEmit('SetUserId', getCookie('gh_id'));
    } catch (e) {
        isLogin.value = false;
    }
};
const back = () => {
    register.value = false
}
const close = () => {
    isLogin.value = false;
}

</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.login {
    position: relative;
    width: 100%;
    height: 100%;
    background: url("@/assets/images/login.jpg");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .loginForm {
        padding: 15px 43px 10px 43px;

        .title {
            margin: 0px auto 40px auto;
            text-align: center;
            font-size: 24px;
            font-family: "PingFangSC-Medium", "PingFang SC";
            font-weight: 500;
            color: #3de9fa;
            text-shadow: 0 2px 8px rgba(61, 233, 250, 0.3);

            h3 {
                font-size: 28px;
                font-weight: 600;
                margin: 0;
                text-shadow: 0 2px 8px rgba(61, 233, 250, 0.5);
            }
        }

        :deep(.el-form-item) {
            margin-bottom: 24px;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                right: 0;
                bottom: -1px;
                height: 1px;
                background: linear-gradient(90deg, transparent 0%, rgba(61, 233, 250, 0.3) 50%, transparent 100%);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            &:focus-within::before {
                opacity: 1;
            }
        }

        :deep(.el-form-item__label) {
            color: #E6F4FF !important;
            font-weight: 500;
            font-size: 15px !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            position: relative;
            text-align: justify;
            text-align-last: justify;
            line-height: 1.5;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 8px;

            &::after {
                content: '';
                position: absolute;
                left: -8px;
                top: 50%;
                transform: translateY(-50%);
                width: 3px;
                height: 16px;
                background: linear-gradient(180deg, #3de9fa 0%, #1196fc 100%);
                border-radius: 2px;
                opacity: 0;
                transition: opacity 0.3s ease;
            }
        }

        :deep(.el-form-item:focus-within .el-form-item__label::after) {
            opacity: 1;
        }

        :deep(.el-input) {
            position: relative;

            .el-input__wrapper {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.7) 0%, rgba(24, 64, 104, 0.5) 100%) !important;
                border: 1px solid rgba(61, 233, 250, 0.25) !important;
                border-radius: 10px !important;
                box-shadow:
                    inset 0 2px 6px rgba(0, 0, 0, 0.3),
                    0 1px 0 rgba(255, 255, 255, 0.05) !important;
                backdrop-filter: blur(8px) !important;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
                height: 48px !important;
                position: relative;
                overflow: hidden;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(61, 233, 250, 0.1), transparent);
                    transition: left 0.6s ease;
                }

                &:hover {
                    border-color: rgba(61, 233, 250, 0.4) !important;
                    box-shadow:
                        inset 0 2px 6px rgba(0, 0, 0, 0.3),
                        0 0 16px rgba(61, 233, 250, 0.15),
                        0 1px 0 rgba(255, 255, 255, 0.08) !important;
                    transform: translateY(-1px);

                    &::before {
                        left: 100%;
                    }
                }

                &.is-focus {
                    border-color: #3de9fa !important;
                    box-shadow:
                        inset 0 2px 6px rgba(0, 0, 0, 0.3),
                        0 0 20px rgba(61, 233, 250, 0.25),
                        0 0 40px rgba(61, 233, 250, 0.1),
                        0 1px 0 rgba(255, 255, 255, 0.1) !important;
                    transform: translateY(-2px);
                }
            }

            .el-input__inner {
                color: #E6F4FF !important;
                font-size: 15px !important;
                font-weight: 400;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                padding-left: 16px !important;

                &::placeholder {
                    color: rgba(230, 244, 255, 0.4) !important;
                    font-weight: 300;
                }
            }
        }

        .login_btn {
            width: 100%;
            height: 52px;
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.85) 0%, rgba(17, 150, 252, 0.95) 100%) !important;
            border: 1px solid rgba(61, 233, 250, 0.4) !important;
            border-radius: 12px !important;
            color: #ffffff !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
            box-shadow:
                0 6px 20px rgba(61, 233, 250, 0.25),
                0 2px 8px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative;
            overflow: hidden;
            margin-top: 8px;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
                transition: left 0.8s ease;
            }

            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
                border-radius: 12px;
                pointer-events: none;
            }

            &:hover {
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.95) 0%, rgba(17, 150, 252, 1) 100%) !important;
                border-color: rgba(61, 233, 250, 0.6) !important;
                box-shadow:
                    0 8px 28px rgba(61, 233, 250, 0.35),
                    0 4px 12px rgba(0, 0, 0, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3),
                    inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;
                transform: translateY(-3px) !important;

                &::before {
                    left: 100%;
                }
            }

            &:active {
                transform: translateY(-1px) !important;
                box-shadow:
                    0 4px 16px rgba(61, 233, 250, 0.3),
                    0 2px 6px rgba(0, 0, 0, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
            }

            &.is-loading {
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.6) 0%, rgba(17, 150, 252, 0.7) 100%) !important;
                cursor: not-allowed;
                transform: none !important;
                opacity: 0.8;

                &:hover {
                    transform: none !important;
                    box-shadow:
                        0 6px 20px rgba(61, 233, 250, 0.25),
                        0 2px 8px rgba(0, 0, 0, 0.3) !important;
                }
            }

            // 添加按钮文字动画
            span {
                position: relative;
                z-index: 2;
                transition: all 0.3s ease;
            }
        }
    }

    .closeBtn {
        position: absolute;
        right: 20px;
        top: 20px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: rgba(255, 71, 87, 0.8);
        background: linear-gradient(135deg, rgba(255, 71, 87, 0.1) 0%, rgba(255, 71, 87, 0.05) 100%);
        border: 1px solid rgba(255, 71, 87, 0.2);
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(4px);
        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.1);

        &:hover {
            color: #ff4757;
            background: linear-gradient(135deg, rgba(255, 71, 87, 0.2) 0%, rgba(255, 71, 87, 0.1) 100%);
            border-color: rgba(255, 71, 87, 0.4);
            transform: scale(1.1);
            box-shadow: 0 4px 16px rgba(255, 71, 87, 0.2);
        }

        &:active {
            transform: scale(0.95);
        }
    }

    :deep(.el-form-item__label) {
        width: 110px !important;
    }
}
</style>
