<template>
  <div class="login-page">
    <div class="page-center">
      <div class="title center">
        <!-- <img src="@/assets/images/logo.png" alt /> -->
        <span>智慧建筑运行管理平台</span>
      </div>

      <div class="container">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.login-page {
  position: relative;
  width: 100%;
  height: 100%;

  .page-center {
    .title {
      position: absolute;
      width: 100%;
      height: auto;
      left: 0;
      right: 0;
      top: 120px;
      margin: 0 auto;
      text-align: center;
      z-index: 10;

      span {
        color: #3de9fa;
        font-size: 42px;
        font-weight: 700;
        font-family: 'DOUYU', 'PingFang SC', sans-serif;
        text-shadow: 0 2px 8px rgba(61, 233, 250, 0.5);
      }
    }

    .container {
      position: absolute;
      margin: 0 auto;
      top: 50%;
      right: 0;
      left: 0;
      transform: translateY(-50%);
      width: 100%;
      max-width: 460px;
      padding: 40px;
      background: linear-gradient(135deg,
        rgba(9, 24, 34, 0.9) 0%,
        rgba(16, 52, 87, 0.8) 50%,
        rgba(24, 64, 104, 0.7) 100%);
      border: 1px solid rgba(61, 233, 250, 0.3);
      border-radius: 16px;
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
      box-shadow:
        0 16px 40px rgba(0, 0, 0, 0.5),
        0 4px 16px rgba(61, 233, 250, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
  }
}

@media (max-width: 768px) {
  .login-page {
    .page-center {
      .title {
        top: 80px;
        padding: 0 20px;

        span {
          font-size: 28px;
        }
      }

      .container {
        width: calc(100% - 40px);
        max-width: none;
        padding: 30px 20px;
        top: 45%;
      }
    }
  }
}
</style>
