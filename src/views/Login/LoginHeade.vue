<template>
  <div class="login-page">
    <div class="page-center">
      <div class="title center">
        <!-- <img src="@/assets/images/logo.png" alt /> -->
        <span class="title-text">智慧建筑运行管理平台</span>
      </div>

      <div class="container">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.login-page {
  position: relative;
  width: 100%;
  height: 100%;

  .page-center {
    .title {
      position: absolute;
      width: 100%;
      max-width: 600px;
      height: auto;
      left: 0;
      right: 0;
      top: 120px;
      margin: 0 auto;
      text-align: center;
      z-index: 10;

      .title-text {
        display: inline-block;
        color: #ffffff;
        font-size: 42px;
        font-weight: 700;
        font-family: 'DOUYU', 'PingFang SC', sans-serif;
        text-shadow:
          0 0 20px rgba(61, 233, 250, 0.6),
          0 4px 8px rgba(0, 0, 0, 0.8),
          0 2px 4px rgba(0, 0, 0, 0.6);
        background: linear-gradient(135deg, #ffffff 0%, #3de9fa 50%, #1196fc 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: titleGlow 3s ease-in-out infinite alternate;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(61, 233, 250, 0.1) 0%, transparent 100%);
          border-radius: 8px;
          z-index: -1;
          opacity: 0;
          animation: titleBg 3s ease-in-out infinite alternate;
        }
      }
    }

    .container {
      position: absolute;
      margin: 0 auto;
      top: 50%;
      right: 0;
      left: 0;
      transform: translateY(-50%);
      width: 100%;
      max-width: 460px;
      padding: 40px;
      background: linear-gradient(135deg,
        rgba(9, 24, 34, 0.95) 0%,
        rgba(16, 52, 87, 0.9) 50%,
        rgba(24, 64, 104, 0.85) 100%);
      border: 1px solid rgba(61, 233, 250, 0.3);
      border-radius: 20px;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.6),
        0 8px 32px rgba(61, 233, 250, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
      position: relative;
      overflow: hidden;

      // 添加内部光效
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
          rgba(61, 233, 250, 0.05) 0%,
          transparent 30%,
          rgba(17, 150, 252, 0.03) 100%);
        border-radius: 20px;
        pointer-events: none;
      }

      // 边框动画效果
      &::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg,
          rgba(61, 233, 250, 0.4) 0%,
          rgba(17, 150, 252, 0.2) 25%,
          transparent 50%,
          rgba(61, 233, 250, 0.2) 75%,
          rgba(61, 233, 250, 0.4) 100%);
        border-radius: 22px;
        z-index: -1;
        opacity: 0;
        animation: borderGlow 4s ease-in-out infinite;
      }
    }
  }
}

// 动画效果
@keyframes titleGlow {
  0% {
    text-shadow:
      0 0 20px rgba(61, 233, 250, 0.6),
      0 4px 8px rgba(0, 0, 0, 0.8),
      0 2px 4px rgba(0, 0, 0, 0.6);
  }
  100% {
    text-shadow:
      0 0 30px rgba(61, 233, 250, 0.8),
      0 0 40px rgba(17, 150, 252, 0.4),
      0 4px 8px rgba(0, 0, 0, 0.8),
      0 2px 4px rgba(0, 0, 0, 0.6);
  }
}

@keyframes titleBg {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 0.3;
  }
}

@keyframes borderGlow {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 0.6;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-page {
    .page-center {
      .title {
        top: 80px;
        padding: 0 20px;

        .title-text {
          font-size: 28px;
        }
      }

      .container {
        width: calc(100% - 40px);
        max-width: none;
        padding: 30px 20px;
        top: 45%;
      }
    }
  }
}

@media (max-width: 480px) {
  .login-page {
    .page-center {
      .title {
        .title-text {
          font-size: 24px;
        }
      }

      .container {
        padding: 25px 15px;
      }
    }
  }
}
</style>
